#!/bin/bash

echo "🧪 测试UI修复效果..."

# 创建测试日志文件
echo "📝 创建测试日志文件..."
mkdir -p test_logs

# 创建多个测试文件
echo "2025-08-03 12:14:29 Database connection established (line 35)" > test_logs/access.log
echo "2025-08-03 12:14:30 Application started successfully (line 36)" >> test_logs/access.log
echo "2025-08-03 12:14:33 Database connection established (line 37)" >> test_logs/access.log

echo "2025-08-03 12:14:36 Network timeout (line 38)" > test_logs/app.log
echo "2025-08-03 12:14:39 Garbage collection completed (line 39)" >> test_logs/app.log
echo "2025-08-03 12:14:42 User login: user55 (line 40)" >> test_logs/app.log

echo "2025-08-03 12:14:44 Internal server error: 500 (line 41)" > test_logs/error.log
echo "2025-08-03 12:14:45 Database connection established (line 42)" >> test_logs/error.log
echo "2025-08-03 12:14:46 Network timeout (line 43)" >> test_logs/error.log

echo "2025-08-03 12:34:26 Database connection established (line 1)" > test_logs/realtime.log
echo "2025-08-03 12:34:27 Connection pool nearly full (line 2)" >> test_logs/realtime.log

echo "✅ 测试文件创建完成"
echo ""
echo "🚀 启动LogTUI应用程序..."
echo "📋 测试说明："
echo "   1. 应用启动后应该立即显示第一个文件的内容"
echo "   2. 使用↑↓键切换文件时应该流畅，无乱码"
echo "   3. 每次切换文件都应该正确显示文件内容"
echo "   4. 状态栏应该正确显示当前文件名"
echo "   5. 按q键退出"
echo ""
echo "🎯 开始测试..."

# 启动应用程序
./logtui -path test_logs

echo ""
echo "✅ 测试完成！"
