package ui

import (
	"fmt"
	"time"

	"github.com/rivo/tview"
)

// StatusBar 状态栏组件
type StatusBar struct {
	*tview.TextView
	currentFile string
	lineCount   int
	searchTerm  string
	isPaused    bool
	lastUpdate  time.Time
}

// NewStatusBar 创建新的状态栏组件
func NewStatusBar() *StatusBar {
	textView := tview.NewTextView()

	sb := &StatusBar{
		TextView:   textView,
		lastUpdate: time.Now(),
	}

	// 设置样式
	textView.SetBorder(true).
		SetTitle(" 状态 ").
		SetTitleAlign(tview.AlignLeft)
	textView.SetDynamicColors(true)

	// 初始化显示
	sb.updateDisplay()

	return sb
}

// SetCurrentFile 设置当前文件
func (sb *StatusBar) SetCurrentFile(filename string) {
	sb.currentFile = filename
	sb.updateDisplay()
}

// SetLineCount 设置行数
func (sb *StatusBar) SetLineCount(count int) {
	sb.lineCount = count
	sb.updateDisplay()
}

// SetSearchTerm 设置搜索关键词
func (sb *StatusBar) SetSearchTerm(term string) {
	sb.searchTerm = term
	sb.updateDisplay()
}

// SetPaused 设置暂停状态
func (sb *StatusBar) SetPaused(paused bool) {
	sb.isPaused = paused
	sb.updateDisplay()
}

// updateDisplay 更新状态栏显示
func (sb *StatusBar) updateDisplay() {
	sb.lastUpdate = time.Now()

	var status string

	// 文件信息
	if sb.currentFile != "" {
		status += fmt.Sprintf("[blue]文件:[white] %s  ", sb.currentFile)
	} else {
		status += "[blue]文件:[white] 未选择  "
	}

	// 行数信息
	status += fmt.Sprintf("[blue]行数:[white] %d  ", sb.lineCount)

	// 搜索信息
	if sb.searchTerm != "" {
		status += fmt.Sprintf("[blue]搜索:[yellow] %s[white]  ", sb.searchTerm)
	}

	// 暂停状态
	if sb.isPaused {
		status += "[red]已暂停[white]  "
	} else {
		status += "[green]实时[white]  "
	}

	// 最后更新时间
	status += fmt.Sprintf("[blue]更新:[white] %s", sb.lastUpdate.Format("15:04:05"))

	sb.SetText(status)
}

// ShowMessage 显示临时消息
func (sb *StatusBar) ShowMessage(message string, duration time.Duration) {
	originalText := sb.GetText(false)

	// 显示消息
	sb.SetText(fmt.Sprintf("[yellow]%s[white]", message))

	// 定时恢复原始状态
	go func() {
		time.Sleep(duration)
		sb.SetText(originalText)
	}()
}

// ShowError 显示错误消息
func (sb *StatusBar) ShowError(err error) {
	sb.ShowMessage(fmt.Sprintf("错误: %v", err), 3*time.Second)
}

// ShowSuccess 显示成功消息
func (sb *StatusBar) ShowSuccess(message string) {
	sb.ShowMessage(fmt.Sprintf("✓ %s", message), 2*time.Second)
}

// GetHelpText 获取帮助文本
func (sb *StatusBar) GetHelpText() string {
	return "[blue]快捷键:[white] [yellow]↑↓[white]切换 [yellow]⏎[white]选择 [yellow]s[white]搜索 [yellow]p[white]暂停 [yellow]c[white]清空 [yellow]r[white]刷新 [yellow]i[white]信息 [yellow]?[white]帮助 [yellow]q[white]退出"
}

// GetSearchHelpText 获取搜索模式的帮助文本
func (sb *StatusBar) GetSearchHelpText() string {
	return "[blue]搜索模式:[white] [yellow]⏎[white]确认 [yellow]Tab[white]下一个 [yellow]Esc[white]取消"
}

// GetSearchNavigationHelp 获取搜索导航帮助
func (sb *StatusBar) GetSearchNavigationHelp() string {
	return "[blue]搜索导航:[white] [yellow]n[white]下一个 [yellow]b[white]上一个 [yellow]s[white]新搜索"
}
