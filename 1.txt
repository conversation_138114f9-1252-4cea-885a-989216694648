🎯 项目目标
开发一个终端下运行的、支持多文件查看、关键词高亮、实时 tail 的日志查看工具，便于开发者调试和排查问题，解决传统 vim/tail 不友好、不支持高亮、缺乏搜索交互等问题。

📦 功能规划
MVP（最小可用版本）
模块	功能描述
📁 日志文件列表	左侧列出支持监听的所有日志文件，可用方向键切换
📄 实时日志展示	中间窗口展示当前日志内容，自动滚动
🔍 搜索功能	输入关键词自动高亮匹配行，支持上下切换匹配项
🎨 级别高亮	日志级别（INFO/WARN/ERROR）自动着色
⏸️ 滚动控制	支持暂停滚动（防止刷屏查看历史）
⌨️ 快捷键支持	q=退出，s=搜索，p=暂停，↑↓ 切换文件

可选扩展功能（阶段二）
功能	描述
🔑 文件夹监控	自动监听新日志文件
🔒 简单密码保护	防止他人使用
⏳ 时间范围筛选	查看某时间段的日志
📄 历史搜索记录	快速切换已搜索关键词
🧪 过滤器配置	仅显示包含关键词的行（vs 高亮）

🧱 技术方案
核心组件
模块	技术
TUI 框架	tview（UI 构建）
文件监听	hpcloud/tail（tail 文件）
并发处理	goroutine + channel
日志高亮	ANSI 颜色 + 正则匹配
快捷键绑定	tview.InputCapture

目录结构建议
csharp
复制
编辑
logtui/
├── cmd/                # 命令入口
│   └── main.go
├── internal/
│   ├── ui/             # tview 相关 UI 逻辑
│   ├── tailer/         # 日志 tail 与 channel 管理
│   ├── search/         # 搜索与高亮模块
│   └── utils/
├── assets/             # 可选：配置文件、颜色模板等
├── go.mod
├── README.md
└── LICENSE
🚀 开发计划（7~10 天）
天数	任务描述
Day 1	项目初始化 + 搭建主界面 UI 结构（左右栏 +主窗口）
Day 2	集成 tail 文件监听，日志实时输出
Day 3	增加日志滚动展示逻辑（自动+暂停）
Day 4	实现文件切换 + 日志级别颜色高亮
Day 5	实现搜索关键词功能，匹配项高亮
Day 6	添加快捷键提示栏 + 重构组件划分
Day 7	添加配置管理、README 文档、构建打包
Day 8~10	扩展功能（历史搜索、目录监听）、测试与优化

📄 README 初始内容建议
markdown
复制
编辑
# LogTUI – 终端下的日志可视化工具

LogTUI 是一个基于 Go 编写的终端日志查看器，支持：

- 多日志文件切换
- 实时日志 tail 输出
- 关键词搜索与高亮
- 日志级别着色（INFO/WARN/ERROR）
- 快捷键控制（搜索、暂停滚动、退出）

## 使用方法

```bash
go build -o logtui
./logtui -path ./logs/
快捷键
↑↓ 文件切换

s 搜索关键词

p 暂停滚动

q 退出程序


性能问题：

1. 性能总体预期：中等偏高，适合中小型应用日志量
场景	性能表现（TUI 工具）
实时追踪写入速度快的日志	✅ 可跟上 10,000 行/秒以上
多个日志文件切换	✅ 快速响应，内存持久缓存
高频关键词搜索	⚠️ 依赖实现，正则会稍慢
日志超大（500MB+）	⚠️ 初始加载慢，需优化

🧪 实测基准（正常实现下）
指标	说明
单文件 tail 实时更新性能	可轻松跟上 2w 行/秒 的日志写入速度（受限于 terminal 渲染）
文件切换响应时间	100ms 内（预加载+缓存）
高亮搜索耗时	实时匹配 < 200ms（10w 行以内）
内存占用	稳定在几十 MB，除非缓冲区设置过大

🔥 性能瓶颈与优化方案
💥 瓶颈1：高频写入大文件的 UI 刷新卡顿
tview 每次 UI 更新都会触发 terminal 重绘，可能产生「闪屏」「卡顿」

✅ 优化方法：

批量 append 行，再隔 100ms 批量刷新 UI（节流）

日志窗口使用 ring buffer，只保留最近 N 行（如 5000 行）

💥 瓶颈2：大文件初次读取慢
若你支持加载历史日志文件，初次打开几百 MB 会很慢

✅ 优化方法：

默认只读最后几千行（tail 模式）

支持延迟加载历史行（翻页再加载）

💥 瓶颈3：搜索/高亮太慢
正则搜索在大文件中性能开销较大

✅ 优化方法：

只在可视区域（当前窗口）内做高亮

使用字符串 contains + index 替代复杂正则

分批异步搜索 + 高亮渲染

✅ Golang 本身的性能优势
高并发：日志 tail/搜索/渲染可并发执行

内存管理稳定：通过 channel 和 buffer 控制压力

编译型：生成的 binary 启动快、CPU 占用低

✅ Tview 性能如何？
tview 是基于 termui 重写的现代 TUI 框架，它性能远比早期框架（如 curses）好，特点如下：

优势	说明
支持异步 UI 更新	不阻塞主线程，可配合 goroutine 高效运行
支持自定义视图刷新	可控制刷新频率
高度优化的组件系统	非常适合做 table, textView 等大量数据场景

💡 项目建议配置（性能相关）
go
复制
编辑
const MaxLogLines = 5000       // 保留最多 5000 行日志，滚动淘汰
const UpdateInterval = 100 * time.Millisecond // UI 每100ms刷新
const SearchMaxLines = 10000   // 搜索仅在可见区域 + 1w 行范围内
🧠 性能总结
维度	表现
响应性	👍 快速、不卡顿（适当节流）
并发能力	👍 tail/search/UI 分离并发
渲染能力	👍 tview 渲染高效，支持异步
内存管理	👍 可控制缓存，资源占用稳定
极限下性能（100w+行）	⚠️ 需分页 + ring buffer

🚀 适用场景总结
使用场景	适合吗？	原因
本地开发查看日志	✅✅✅	快速启动，响应快
线上服务器 tail 多文件	✅✅	轻量部署，多窗口查看
替代 ELK、支持全文分析	❌	不适合大规模聚合查询
产品/业务日常排查	✅✅✅	支持过滤、高亮、快捷

