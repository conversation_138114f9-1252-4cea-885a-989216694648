package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"logtui/internal/ui"
)

var (
	version     = "1.0.0"
	logPath     = flag.String("path", "./", "日志文件路径")
	showVersion = flag.Bool("version", false, "显示版本信息")
	showHelp    = flag.Bool("help", false, "显示帮助信息")
)

func main() {
	flag.Parse()

	// 显示版本信息
	if *showVersion {
		fmt.Printf("LogTUI v%s\n", version)
		fmt.Println("终端下的日志可视化工具")
		os.Exit(0)
	}

	// 显示帮助信息
	if *showHelp {
		showHelpMessage()
		os.Exit(0)
	}

	// 验证路径
	absPath, err := filepath.Abs(*logPath)
	if err != nil {
		log.Fatalf("无效的路径: %v", err)
	}

	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		log.Fatalf("路径不存在: %s", absPath)
	}

	// 创建并运行应用程序
	app := ui.NewApp()

	// 设置日志路径
	if err := app.SetLogPath(absPath); err != nil {
		log.Fatalf("设置日志路径失败: %v", err)
	}

	// 运行应用程序
	if err := app.Run(); err != nil {
		log.Fatalf("应用程序运行失败: %v", err)
	}
}

func showHelpMessage() {
	fmt.Printf("LogTUI v%s - 终端下的日志可视化工具\n\n", version)
	fmt.Println("使用方法:")
	fmt.Printf("  %s [选项]\n\n", os.Args[0])
	fmt.Println("选项:")
	fmt.Println("  -path string")
	fmt.Println("        日志文件路径 (默认: ./)")
	fmt.Println("  -version")
	fmt.Println("        显示版本信息")
	fmt.Println("  -help")
	fmt.Println("        显示此帮助信息")
	fmt.Println()
	fmt.Println("快捷键:")
	fmt.Println("  ↑↓     切换文件")
	fmt.Println("  s      搜索关键词")
	fmt.Println("  p      暂停/恢复滚动")
	fmt.Println("  c      清空日志")
	fmt.Println("  r      刷新文件列表")
	fmt.Println("  q      退出程序")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Printf("  %s -path /var/log\n", os.Args[0])
	fmt.Printf("  %s -path ./logs\n", os.Args[0])
}
