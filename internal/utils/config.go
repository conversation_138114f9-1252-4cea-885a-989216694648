package utils

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// Config 应用配置
type Config struct {
	MaxLines        int    `json:"max_lines"`         // 最大保留行数
	UpdateInterval  int    `json:"update_interval"`   // 更新间隔(毫秒)
	HistoryLines    int    `json:"history_lines"`     // 历史加载行数
	SearchCaseSensitive bool `json:"search_case_sensitive"` // 搜索是否区分大小写
	AutoScroll      bool   `json:"auto_scroll"`       // 默认自动滚动
	Theme           string `json:"theme"`             // 主题
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		MaxLines:            1000,
		UpdateInterval:      50,
		HistoryLines:        50,
		SearchCaseSensitive: false,
		AutoScroll:          true,
		Theme:               "default",
	}
}

// LoadConfig 加载配置文件
func LoadConfig() (*Config, error) {
	configPath := getConfigPath()
	
	// 如果配置文件不存在，返回默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return DefaultConfig(), nil
	}
	
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}
	
	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}
	
	// 验证配置值
	config.validate()
	
	return &config, nil
}

// SaveConfig 保存配置文件
func (c *Config) SaveConfig() error {
	configPath := getConfigPath()
	
	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %v", err)
	}
	
	// 验证配置值
	c.validate()
	
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}
	
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}
	
	return nil
}

// validate 验证配置值
func (c *Config) validate() {
	if c.MaxLines < 100 {
		c.MaxLines = 100
	}
	if c.MaxLines > 10000 {
		c.MaxLines = 10000
	}
	
	if c.UpdateInterval < 10 {
		c.UpdateInterval = 10
	}
	if c.UpdateInterval > 1000 {
		c.UpdateInterval = 1000
	}
	
	if c.HistoryLines < 10 {
		c.HistoryLines = 10
	}
	if c.HistoryLines > 1000 {
		c.HistoryLines = 1000
	}
}

// getConfigPath 获取配置文件路径
func getConfigPath() string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "./logtui.json"
	}
	
	return filepath.Join(homeDir, ".config", "logtui", "config.json")
}

// GetLogFileExtensions 获取支持的日志文件扩展名
func GetLogFileExtensions() []string {
	return []string{".log", ".txt", ".out", ".err"}
}

// GetLogFileKeywords 获取日志文件关键词
func GetLogFileKeywords() []string {
	return []string{"log", "access", "error", "debug", "trace"}
}
