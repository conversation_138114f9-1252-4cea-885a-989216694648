# LogTUI – 终端下的日志可视化工具

LogTUI 是一个基于 Go 编写的终端日志查看器，提供友好的TUI界面，支持多文件切换、实时监控、关键词搜索和日志级别着色等功能。

## ✨ 功能特性

- 📁 **多文件支持** - 左侧面板列出所有日志文件，支持快速切换
- 📄 **实时监控** - 自动tail文件变化，实时显示新增日志
- 🔍 **关键词搜索** - 支持关键词高亮，快速定位重要信息
- 🎨 **级别着色** - 自动识别日志级别（INFO/WARN/ERROR）并着色
- ⏸️ **滚动控制** - 支持暂停滚动，方便查看历史日志
- ⌨️ **快捷键操作** - 丰富的快捷键支持，提高使用效率

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd LogTUI

# 构建
go build -o logtui cmd/main.go

# 运行
./logtui -path /var/log
```

### 使用方法

```bash
# 监控当前目录的日志文件
./logtui

# 监控指定目录的日志文件
./logtui -path /var/log

# 显示帮助信息
./logtui -help

# 显示版本信息
./logtui -version
```

## ⌨️ 快捷键

| 快捷键 | 功能 |
|--------|------|
| `↑↓` | 切换文件 |
| `s` | 搜索关键词 |
| `p` | 暂停/恢复滚动 |
| `c` | 清空日志显示 |
| `r` | 刷新文件列表 |
| `q` | 退出程序 |
| `ESC` | 退出搜索模式 |

## 🎯 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                        LogTUI v1.0.0                       │
├──────────────┬──────────────────────────────────────────────┤
│  📁 日志文件  │              📄 日志内容                     │
│              │                                              │
│ ▶ app.log    │ 2024-08-03 10:30:15 [INFO] Application      │
│   error.log  │ started successfully                        │
│   access.log │ 2024-08-03 10:30:16 [WARN] High memory      │
│              │ usage detected                              │
│              │ 2024-08-03 10:30:17 [ERROR] Database        │
│              │ connection failed                           │
├──────────────┴──────────────────────────────────────────────┤
│ 📊 状态: 文件: app.log  行数: 1234  搜索: error  实时  10:30:17 │
├─────────────────────────────────────────────────────────────┤
│ 💡 快捷键: ↑↓切换文件 s搜索 p暂停 c清空 q退出                │
└─────────────────────────────────────────────────────────────┘
```

## 🎨 日志级别着色

- 🔴 **ERROR/FATAL** - 红色显示
- 🟡 **WARN/WARNING** - 黄色显示  
- 🟢 **INFO** - 绿色显示
- 🔵 **DEBUG/TRACE** - 蓝色显示

## 📁 支持的文件类型

LogTUI 自动识别以下类型的日志文件：

- `.log` - 标准日志文件
- `.txt` - 文本日志文件
- `.out` - 输出日志文件
- `.err` - 错误日志文件
- 文件名包含 "log" 的文件

## 🔧 配置选项

### 命令行参数

- `-path` - 指定日志文件路径（默认：当前目录）
- `-version` - 显示版本信息
- `-help` - 显示帮助信息

## 🏗️ 项目结构

```
logtui/
├── cmd/                # 命令入口
│   └── main.go
├── internal/
│   ├── ui/             # tview 相关 UI 逻辑
│   │   ├── app.go      # 主应用程序
│   │   ├── filelist.go # 文件列表组件
│   │   ├── logview.go  # 日志展示组件
│   │   └── statusbar.go# 状态栏组件
│   ├── tailer/         # 日志 tail 与 channel 管理
│   ├── search/         # 搜索与高亮模块
│   └── utils/          # 工具函数
├── assets/             # 配置文件、颜色模板等
├── go.mod
├── README.md
└── LICENSE
```

## 🛠️ 技术栈

- **TUI框架**: [tview](https://github.com/rivo/tview) - 强大的终端UI库
- **文件监听**: [hpcloud/tail](https://github.com/hpcloud/tail) - 文件tail功能
- **并发处理**: goroutine + channel - Go原生并发支持
- **终端控制**: [tcell](https://github.com/gdamore/tcell) - 终端事件处理

## 📋 开发计划

- [x] 项目初始化和基础UI结构
- [ ] 集成文件tail监听功能
- [ ] 实现日志实时显示
- [ ] 添加搜索和高亮功能
- [ ] 完善快捷键和交互
- [ ] 添加配置文件支持
- [ ] 性能优化和测试

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
