package tailer

import (
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/hpcloud/tail"
)

// LogLine 日志行结构
type LogLine struct {
	Text      string
	Timestamp time.Time
	Filename  string
}

// Tailer 文件监听器
type Tailer struct {
	mutex       sync.RWMutex
	tail        *tail.Tail
	filename    string
	isRunning   bool
	outputChan  chan LogLine
	stopChan    chan struct{}
	error<PERSON>han   chan error
}

// NewTailer 创建新的文件监听器
func NewTailer() *Tailer {
	return &Tailer{
		outputChan: make(chan LogLine, 100), // 缓冲100行
		stopChan:   make(chan struct{}),
		errorChan:  make(chan error, 10),
	}
}

// Start 开始监听指定文件
func (t *Tailer) Start(filename string) error {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	// 如果已经在运行，先停止
	if t.isRunning {
		t.stopInternal()
	}

	// 检查文件是否存在
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", filename)
	}

	// 创建tail配置
	config := tail.Config{
		Follow:    true,  // 跟随文件变化
		ReOpen:    true,  // 文件被重新创建时重新打开
		MustExist: true,  // 文件必须存在
		Poll:      false, // 使用inotify而不是轮询
		Location:  &tail.SeekInfo{Offset: 0, Whence: 2}, // 从文件末尾开始
	}

	// 创建tail实例
	tailInstance, err := tail.TailFile(filename, config)
	if err != nil {
		return fmt.Errorf("创建tail实例失败: %v", err)
	}

	t.tail = tailInstance
	t.filename = filename
	t.isRunning = true

	// 启动监听goroutine
	go t.watchLoop()

	return nil
}

// watchLoop 监听循环
func (t *Tailer) watchLoop() {
	defer func() {
		t.mutex.Lock()
		t.isRunning = false
		t.mutex.Unlock()
	}()

	for {
		select {
		case <-t.stopChan:
			return

		case line, ok := <-t.tail.Lines:
			if !ok {
				// channel已关闭
				return
			}

			if line.Err != nil {
				// 发送错误到错误channel
				select {
				case t.errorChan <- line.Err:
				default:
					// 错误channel满了，忽略
				}
				continue
			}

			// 创建日志行对象
			logLine := LogLine{
				Text:      line.Text,
				Timestamp: line.Time,
				Filename:  t.filename,
			}

			// 发送到输出channel
			select {
			case t.outputChan <- logLine:
			case <-t.stopChan:
				return
			default:
				// 输出channel满了，丢弃最旧的行
				select {
				case <-t.outputChan:
				default:
				}
				select {
				case t.outputChan <- logLine:
				case <-t.stopChan:
					return
				}
			}
		}
	}
}

// Stop 停止监听
func (t *Tailer) Stop() {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	t.stopInternal()
}

// stopInternal 内部停止方法（不加锁）
func (t *Tailer) stopInternal() {
	if !t.isRunning {
		return
	}

	// 发送停止信号
	close(t.stopChan)

	// 停止tail
	if t.tail != nil {
		t.tail.Stop()
		t.tail.Cleanup()
		t.tail = nil
	}

	// 重新创建停止channel
	t.stopChan = make(chan struct{})
	t.isRunning = false
}

// GetOutputChan 获取输出channel
func (t *Tailer) GetOutputChan() <-chan LogLine {
	return t.outputChan
}

// GetErrorChan 获取错误channel
func (t *Tailer) GetErrorChan() <-chan error {
	return t.errorChan
}

// IsRunning 检查是否正在运行
func (t *Tailer) IsRunning() bool {
	t.mutex.RLock()
	defer t.mutex.RUnlock()
	return t.isRunning
}

// GetCurrentFile 获取当前监听的文件
func (t *Tailer) GetCurrentFile() string {
	t.mutex.RLock()
	defer t.mutex.RUnlock()
	return t.filename
}

// TailerManager 监听器管理器
type TailerManager struct {
	mutex   sync.RWMutex
	tailers map[string]*Tailer
	current *Tailer
}

// NewTailerManager 创建监听器管理器
func NewTailerManager() *TailerManager {
	return &TailerManager{
		tailers: make(map[string]*Tailer),
	}
}

// SwitchFile 切换到指定文件
func (tm *TailerManager) SwitchFile(filename string) (*Tailer, error) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 停止当前监听器
	if tm.current != nil {
		tm.current.Stop()
	}

	// 获取或创建监听器
	tailer, exists := tm.tailers[filename]
	if !exists {
		tailer = NewTailer()
		tm.tailers[filename] = tailer
	}

	// 启动监听器
	if err := tailer.Start(filename); err != nil {
		return nil, err
	}

	tm.current = tailer
	return tailer, nil
}

// GetCurrent 获取当前监听器
func (tm *TailerManager) GetCurrent() *Tailer {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()
	return tm.current
}

// StopAll 停止所有监听器
func (tm *TailerManager) StopAll() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	for _, tailer := range tm.tailers {
		tailer.Stop()
	}

	tm.current = nil
}

// Cleanup 清理资源
func (tm *TailerManager) Cleanup() {
	tm.StopAll()
	tm.tailers = make(map[string]*Tailer)
}
