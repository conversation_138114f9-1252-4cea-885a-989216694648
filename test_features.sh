#!/bin/bash

# LogTUI 功能测试脚本

echo "🚀 LogTUI 功能测试"
echo "=================="

# 创建测试日志目录
mkdir -p test_logs

# 创建不同类型的测试日志文件
echo "📝 创建测试日志文件..."

# 应用日志
cat > test_logs/app.log << 'EOF'
2025-08-03 10:00:01 [INFO] Application started successfully
2025-08-03 10:00:02 [INFO] Loading configuration from config.yaml
2025-08-03 10:00:03 [INFO] Database connection established
2025-08-03 10:00:04 [WARN] High memory usage detected: 85%
2025-08-03 10:00:05 [INFO] HTTP server listening on port 8080
2025-08-03 10:00:06 [ERROR] Failed to connect to Redis: connection refused
2025-08-03 10:00:07 [WARN] Retrying Redis connection in 5 seconds
2025-08-03 10:00:08 [INFO] Redis connection established
2025-08-03 10:00:09 [DEBUG] Processing user request: GET /api/users
2025-08-03 10:00:10 [INFO] Request completed: 200 OK
EOF

# 错误日志
cat > test_logs/error.log << 'EOF'
2025-08-03 10:00:06 [ERROR] Failed to connect to Redis: connection refused
2025-08-03 10:00:15 [ERROR] Database query timeout after 30s
2025-08-03 10:00:16 [ERROR] Internal server error: 500
2025-08-03 10:00:25 [FATAL] Critical system failure detected
2025-08-03 10:00:26 [ERROR] Unable to recover from system failure
EOF

# 访问日志
cat > test_logs/access.log << 'EOF'
192.168.1.100 - - [03/Aug/2025:10:00:01 +0000] "GET /api/users HTTP/1.1" 200 1234
192.168.1.101 - - [03/Aug/2025:10:00:02 +0000] "POST /api/login HTTP/1.1" 200 567
192.168.1.102 - - [03/Aug/2025:10:00:03 +0000] "GET /api/products HTTP/1.1" 200 2345
192.168.1.100 - - [03/Aug/2025:10:00:04 +0000] "GET /api/users HTTP/1.1" 429 89
192.168.1.103 - - [03/Aug/2025:10:00:05 +0000] "POST /api/orders HTTP/1.1" 500 0
EOF

# 调试日志
cat > test_logs/debug.log << 'EOF'
2025-08-03 10:00:01 [DEBUG] Starting application initialization
2025-08-03 10:00:02 [DEBUG] Loading modules: auth, database, cache
2025-08-03 10:00:03 [DEBUG] Auth module loaded successfully
2025-08-03 10:00:04 [DEBUG] Database module loaded successfully
2025-08-03 10:00:05 [DEBUG] Cache module loaded successfully
2025-08-03 10:00:06 [TRACE] Memory usage: 45MB
2025-08-03 10:00:07 [TRACE] CPU usage: 12%
2025-08-03 10:00:08 [DEBUG] Application ready to serve requests
EOF

echo "✅ 测试文件创建完成"
echo ""

echo "📋 功能测试指南:"
echo "================"
echo ""
echo "1. 🚀 启动应用:"
echo "   ./logtui -path test_logs"
echo ""
echo "2. 📁 文件切换测试:"
echo "   - 使用 ↑↓ 键切换文件"
echo "   - 按 Enter 选择文件"
echo "   - 观察文件图标和大小显示"
echo ""
echo "3. 🔍 搜索功能测试:"
echo "   - 按 's' 进入搜索模式"
echo "   - 输入 'ERROR' 搜索错误日志"
echo "   - 按 Enter 确认搜索"
echo "   - 使用 'n' 和 'b' 导航搜索结果"
echo ""
echo "4. ⏸️ 滚动控制测试:"
echo "   - 按 'p' 暂停/恢复自动滚动"
echo "   - 观察标题栏状态变化"
echo ""
echo "5. ℹ️ 信息显示测试:"
echo "   - 按 'i' 显示当前文件信息"
echo "   - 按 '?' 显示帮助信息"
echo ""
echo "6. 🔄 刷新测试:"
echo "   - 按 'r' 刷新文件列表"
echo ""
echo "7. 🧹 清空测试:"
echo "   - 按 'c' 清空日志显示"
echo ""
echo "8. 🎨 颜色测试:"
echo "   - 观察不同日志级别的颜色:"
echo "     • ERROR/FATAL: 红色"
echo "     • WARN: 黄色"
echo "     • INFO: 绿色"
echo "     • DEBUG/TRACE: 蓝色"
echo ""
echo "9. 🔄 实时监听测试:"
echo "   - 在另一个终端运行:"
echo "     echo '2025-08-03 10:30:00 [INFO] 新的日志消息' >> test_logs/app.log"
echo "   - 观察日志是否实时更新"
echo ""
echo "10. 🚪 退出测试:"
echo "    - 按 'q' 退出应用"
echo ""

echo "🎯 预期行为:"
echo "============"
echo "✅ 文件列表显示所有日志文件及其图标"
echo "✅ 选择文件后显示历史日志内容"
echo "✅ 搜索功能正确高亮匹配项"
echo "✅ 搜索导航显示当前位置 (x/y)"
echo "✅ 暂停功能正确控制滚动"
echo "✅ 状态栏显示文件信息和状态"
echo "✅ 帮助信息根据上下文动态变化"
echo "✅ 日志级别正确着色"
echo "✅ 实时监听新增日志行"
echo "✅ 所有快捷键响应正常"
echo ""

echo "🐛 如果发现问题:"
echo "================"
echo "1. 检查终端是否支持颜色显示"
echo "2. 确保有足够的终端窗口大小"
echo "3. 验证日志文件权限"
echo "4. 检查Go版本兼容性"
echo ""

echo "准备就绪! 运行 './logtui -path test_logs' 开始测试 🎉"
