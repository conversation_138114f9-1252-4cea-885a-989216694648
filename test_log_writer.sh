#!/bin/bash

# 测试日志写入脚本
# 用于测试LogTUI的实时监听功能

LOG_FILE="test_logs/realtime.log"

# 创建日志文件
touch "$LOG_FILE"

echo "开始向 $LOG_FILE 写入测试日志..."
echo "请在另一个终端运行: ./logtui -path test_logs"
echo "然后选择 realtime.log 文件"
echo "按 Ctrl+C 停止写入"

# 计数器
counter=1

while true; do
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 随机选择日志级别
    levels=("INFO" "WARN" "ERROR" "DEBUG")
    level=${levels[$((RANDOM % 4))]}
    
    # 随机选择消息
    case $level in
        "INFO")
            messages=(
                "Application started successfully"
                "User login: user$((RANDOM % 100))"
                "Request processed successfully"
                "Database connection established"
                "Cache updated"
                "Health check passed"
            )
            ;;
        "WARN")
            messages=(
                "High memory usage detected: $((70 + RANDOM % 20))%"
                "Rate limit exceeded for IP: 192.168.1.$((RANDOM % 255))"
                "Slow query detected: $((RANDOM % 1000))ms"
                "Connection pool nearly full"
            )
            ;;
        "ERROR")
            messages=(
                "Database connection failed"
                "Internal server error: 500"
                "Authentication failed"
                "File not found: /tmp/missing.txt"
                "Network timeout"
            )
            ;;
        "DEBUG")
            messages=(
                "Processing request ID: $((RANDOM % 10000))"
                "Query executed in $((RANDOM % 100))ms"
                "Cache hit for key: user_$((RANDOM % 1000))"
                "Garbage collection completed"
            )
            ;;
    esac
    
    message=${messages[$((RANDOM % ${#messages[@]}))]}
    
    # 写入日志
    echo "$timestamp [$level] $message (line $counter)" >> "$LOG_FILE"
    
    echo "写入: $timestamp [$level] $message (line $counter)"
    
    counter=$((counter + 1))
    
    # 随机延迟 1-3 秒
    sleep $((1 + RANDOM % 3))
done
